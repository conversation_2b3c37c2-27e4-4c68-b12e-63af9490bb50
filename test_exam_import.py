"""
Script để test chức năng import DOCX
"""

import requests
import json
from docx import Document
import io

def create_test_docx():
    """Tạo file DOCX test"""
    doc = Document()
    
    # Header
    doc.add_heading('BỘ GIÁO DỤC VÀ ĐÀO TẠO', 0)
    doc.add_heading('Trường THPT Hong Thinh', 1)
    doc.add_heading('ĐỀ KIỂM TRA LỚP 12', 1)
    
    # Thông tin đề thi
    doc.add_paragraph('Môn: HOA HOC')
    doc.add_paragraph('Thời gian làm bài: 90 phút, không kể thời gian phát đề')
    doc.add_paragraph('')
    doc.add_paragraph('Họ, tên thí sinh: ..................................................')
    doc.add_paragraph('<PERSON><PERSON> đề: 1234')
    doc.add_paragraph('<PERSON><PERSON> báo danh: .......................................................')
    doc.add_paragraph('')
    
    # Bảng nguyên tử khối
    doc.add_heading('BẢNG NGUYÊN TỬ KHỐI CỦA CÁC NGUYÊN TỐ HÓA HỌC', 2)
    doc.add_paragraph('H = 1; C = 12; N = 14; O = 16; S = 32')
    doc.add_paragraph('')
    
    # Phần I
    doc.add_heading('PHẦN I. Câu trắc nghiệm nhiều phương án lựa chọn', 2)
    doc.add_paragraph('Thí sinh trả lời từ câu 1 đến câu 2. (Mỗi câu trả lời đúng thí sinh được 0,25 điểm)')
    doc.add_paragraph('')
    
    doc.add_paragraph('Câu 1. Hạt nào sau đây không cấu tạo nên hạt nhân nguyên tử?')
    doc.add_paragraph('A. Proton')
    doc.add_paragraph('B. Neutron')
    doc.add_paragraph('C. Electron')
    doc.add_paragraph('D. Cả proton và neutron')
    doc.add_paragraph('')
    
    doc.add_paragraph('Câu 2. Đơn vị khối lượng nguyên tử (amu) tương đương với bao nhiêu kg?')
    doc.add_paragraph('A. 1,6605 x 10^-27 kg')
    doc.add_paragraph('B. 1,6605 x 10^-24 kg')
    doc.add_paragraph('C. 1,602 x 10^-19 kg')
    doc.add_paragraph('D. 9,109 x 10^-31 kg')
    doc.add_paragraph('')
    
    # Phần II
    doc.add_heading('PHẦN II. Câu trắc nghiệm đúng sai', 2)
    doc.add_paragraph('Thí sinh trả lời từ câu 1 đến câu 1.')
    doc.add_paragraph('')
    
    doc.add_paragraph('Câu 1. Xét về cấu tạo nguyên tử Hydrogen (H), cho các phát biểu sau:')
    doc.add_paragraph('a) Nguyên tử Hydrogen chỉ chứa một proton trong hạt nhân.')
    doc.add_paragraph('b) Nguyên tử Hydrogen luôn chứa một neutron trong hạt nhân.')
    doc.add_paragraph('c) Nguyên tử Hydrogen trung hòa về điện vì số proton bằng số electron.')
    doc.add_paragraph('d) Khối lượng của nguyên tử Hydrogen chủ yếu tập trung ở lớp vỏ electron.')
    doc.add_paragraph('')
    
    # Phần III
    doc.add_heading('PHẦN III. Câu trắc nghiệm trả lời ngắn', 2)
    doc.add_paragraph('Thí sinh trả lời từ câu 1 đến câu 1')
    doc.add_paragraph('')
    
    doc.add_paragraph('Câu 1. Một nguyên tử X có cấu tạo gồm 1 proton, 1 electron và không có neutron. Tính khối lượng gần đúng của nguyên tử X theo đơn vị amu.')
    doc.add_paragraph('')
    
    doc.add_paragraph('--- Hết ---')
    doc.add_paragraph('')
    
    # Đáp án
    doc.add_heading('ĐÁP ÁN', 2)
    doc.add_paragraph('')
    
    doc.add_paragraph('PHẦN I. Câu trắc nghiệm nhiều phương án lựa chọn.')
    doc.add_paragraph('Câu    1    2')
    doc.add_paragraph('Chọn   C    A')
    doc.add_paragraph('')
    
    doc.add_paragraph('PHẦN II. Câu trắc nghiệm đúng sai.')
    doc.add_paragraph('Câu 1: a) Đúng, b) Sai, c) Đúng, d) Sai')
    doc.add_paragraph('')
    
    doc.add_paragraph('PHẦN III. Câu trắc nghiệm trả lời ngắn.')
    doc.add_paragraph('Câu 1: 1')
    
    # Lưu vào memory
    file_stream = io.BytesIO()
    doc.save(file_stream)
    file_stream.seek(0)
    
    return file_stream.getvalue()

def test_import_status():
    """Test endpoint import-status"""
    print("=== Testing import-status endpoint ===")
    
    try:
        response = requests.get("http://localhost:8000/api/v1/exam/import-status")
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_import_docx():
    """Test endpoint import-docx"""
    print("\n=== Testing import-docx endpoint ===")
    
    try:
        # Tạo file DOCX test
        print("Creating test DOCX file...")
        docx_content = create_test_docx()
        print(f"DOCX file created, size: {len(docx_content)} bytes")
        
        # Gửi request
        print("Sending import request...")
        files = {
            'file': ('test_exam.docx', docx_content, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
        }
        data = {
            'additional_instructions': 'Đây là file test, hãy phân tích cẩn thận'
        }
        
        response = requests.post(
            "http://localhost:8000/api/v1/exam/import-docx",
            files=files,
            data=data,
            timeout=60
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Import successful!")
            print(f"Success: {result.get('success')}")
            print(f"Message: {result.get('message')}")
            
            if result.get('data'):
                data = result['data']
                print(f"Subject: {data.get('subject')}")
                print(f"Grade: {data.get('grade')}")
                print(f"Duration: {data.get('duration_minutes')} minutes")
                print(f"School: {data.get('school')}")
                print(f"Exam Code: {data.get('exam_code')}")
                print(f"Parts: {len(data.get('parts', []))}")
                
                for i, part in enumerate(data.get('parts', [])):
                    print(f"  Part {i+1}: {part.get('part')} - {len(part.get('questions', []))} questions")
            
            print(f"Processing time: {result.get('processing_time', 0):.2f}s")
            
        else:
            print("❌ Import failed!")
            try:
                error_data = response.json()
                print(f"Error: {error_data.get('error')}")
                print(f"Message: {error_data.get('message')}")
            except:
                print(f"Response text: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_import_docx_test():
    """Test endpoint import-docx-test"""
    print("\n=== Testing import-docx-test endpoint ===")
    
    try:
        response = requests.post("http://localhost:8000/api/v1/exam/import-docx-test")
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Test successful!")
            print(f"Success: {result.get('success')}")
            print(f"Message: {result.get('message')}")
            
            if result.get('analysis_result'):
                analysis = result['analysis_result']
                print(f"Analysis Success: {analysis.get('success')}")
                if analysis.get('data'):
                    print("Analysis data received ✅")
                else:
                    print(f"Analysis Error: {analysis.get('error')}")
        else:
            print("❌ Test failed!")
            print(f"Response: {response.text}")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting DOCX Import Tests")
    print("=" * 50)
    
    # Test 1: Import status
    status_ok = test_import_status()
    
    # Test 2: Import test endpoint
    test_ok = test_import_docx_test()
    
    # Test 3: Import real DOCX
    import_ok = test_import_docx()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"Import Status: {'✅' if status_ok else '❌'}")
    print(f"Import Test: {'✅' if test_ok else '❌'}")
    print(f"Import DOCX: {'✅' if import_ok else '❌'}")
    
    if all([status_ok, test_ok, import_ok]):
        print("\n🎉 All tests passed!")
    else:
        print("\n⚠️ Some tests failed. Check the logs above.")
