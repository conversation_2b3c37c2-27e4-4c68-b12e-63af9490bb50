import requests
import json

# Test data - simplified
test_data = {
    "school": "Trường THPT Hong Thinh",
    "examCode": "1234",
    "grade": 12,
    "subject": "Hoa hoc",
    "examTitle": "Kiể<PERSON> tra ne",
    "duration": 90,
    "outputFormat": "docx",
    "outputLink": "online",
    "matrix": [
        {
            "lessonId": "test1",
            "totalQuestions": 3,
            "parts": [
                {
                    "part": 1,
                    "objectives": {
                        "Biết": 1,
                        "Hiểu": 0,
                        "Vận_dụng": 0
                    }
                },
                {
                    "part": 2,
                    "objectives": {
                        "Biết": 1,
                        "Hiể<PERSON>": 0,
                        "Vận_dụng": 0
                    }
                },
                {
                    "part": 3,
                    "objectives": {
                        "Biết": 0,
                        "Hiểu": 1,
                        "Vận_dụng": 0
                    }
                }
            ]
        }
    ]
}

# Make request
try:
    response = requests.post(
        "http://localhost:8000/api/v1/exam/generate-smart-exam",
        headers={"Content-Type": "application/json"},
        json=test_data,
        timeout=120
    )
    
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"Success: {result}")
    else:
        print(f"Error: {response.text}")
        
except Exception as e:
    print(f"Exception: {e}")
